# نظام إلغاء الطلبات التلقائي

## الوصف
تم إضافة نظام تلقائي لإلغاء الطلبات التي تتجاوز الوقت المحدد للتسليم. هذا النظام يفحص الطلبات ذات الحالة `pending` ويلغيها تلقائياً إذا تجاوزت الوقت المحدد في الإعدادات.

## كيفية العمل

### 1. الإعداد
- يتم تحديد الوقت المسموح للتسليم من خلال إعداد `delivery_deadline_minutes` في جدول الإعدادات
- القيمة الافتراضية هي 30 دقيقة
- يمكن تغيير هذه القيمة من لوحة الإعدادات في الإدارة

### 2. المعالجة التلقائية
- يتم تشغيل فحص تلقائي كل دقيقة للطلبات المنتهية الصلاحية
- الطلبات التي تكون في حالة `pending` وتجاوزت الوقت المحدد يتم تغيير حالتها إلى `canceled_timed_out_by_system`
- يتم إضافة سجل tracking جديد يوضح سبب الإلغاء

### 3. الملفات المضافة/المعدلة

#### الملفات الجديدة:
- `app/Console/Commands/CancelExpiredOrders.php` - الأمر المسؤول عن إلغاء الطلبات المنتهية الصلاحية

#### الملفات المعدلة:
- `app/Services/OrderService.php` - إضافة دالة `cancelExpiredOrders()`
- `routes/console.php` - إضافة المهمة المجدولة

## الاستخدام

### تشغيل الأمر يدوياً
```bash
php artisan orders:cancel-expired
```

### المهمة المجدولة
المهمة تعمل تلقائياً كل دقيقة. للتأكد من تشغيل المهام المجدولة، تأكد من تشغيل:
```bash
php artisan schedule:work
```

أو إضافة cron job:
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### عرض المهام المجدولة
```bash
php artisan schedule:list
```

## الإعدادات

### تغيير وقت انتهاء الصلاحية
يمكن تغيير قيمة `delivery_deadline_minutes` من:
1. لوحة الإعدادات في الإدارة
2. أو مباشرة في قاعدة البيانات:
```sql
UPDATE settings SET value = '60' WHERE key = 'delivery_deadline_minutes';
```

## السجلات
- يتم تسجيل عمليات الإلغاء في ملف السجلات
- يتم إضافة سجل tracking لكل طلب ملغى يوضح سبب الإلغاء
- رسالة الإلغاء: "تم إلغاء الطلب تلقائياً بسبب انتهاء الوقت المحدد للتسليم"

## الاختبار
تم اختبار النظام وهو يعمل بشكل صحيح:
- تم إلغاء 14 طلب منتهي الصلاحية في الاختبار الأول
- المهمة المجدولة تعمل كل دقيقة
- سجلات التتبع تُضاف بشكل صحيح
