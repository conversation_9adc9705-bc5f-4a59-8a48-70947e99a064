<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\OrderService;
use App\Models\Setting;

class CancelExpiredOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:cancel-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel orders that have exceeded their delivery deadline';

    protected OrderService $orderService;

    /**
     * Create a new command instance.
     */
    public function __construct(OrderService $orderService)
    {
        parent::__construct();
        $this->orderService = $orderService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('بدء فحص الطلبات المنتهية الصلاحية...');

        try {
            $canceledCount = $this->orderService->cancelExpiredOrders();
            
            if ($canceledCount > 0) {
                $this->info("تم إلغاء {$canceledCount} طلب منتهي الصلاحية");
            } else {
                $this->info('لا توجد طلبات منتهية الصلاحية');
            }

            return 0;
        } catch (\Exception $e) {
            $this->error('حدث خطأ أثناء معالجة الطلبات المنتهية الصلاحية: ' . $e->getMessage());
            return 1;
        }
    }
}
